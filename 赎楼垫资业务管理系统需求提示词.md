# 二手房交易卖方赎楼垫资业务管理系统开发需求

## 系统概述

请帮我设计并开发一套**二手房交易卖方赎楼垫资业务管理系统**，用于管理从客户推荐到业务完结的全流程。

## 业务背景

### 业务模式
- **核心业务**: 二手房交易中卖方赎楼垫资服务
- **业务流程**: 卖方房产有原贷款 → 需要结清原贷款才能过户 → 卖方资金不足 → 向我司合作金融机构借款 → 结清原贷款注销抵押 → 房产过户 → 收到售房款后归还借款
- **我司角色**: 二手房经纪公司的合作方，提供融资服务对接
- **盈利模式**: 向经纪公司支付推荐返佣，向金融机构收取服务费

### 参与方
1. **我司**: 融资服务提供方
2. **经纪公司**: 客户推荐方
3. **卖方**: 融资需求方
4. **金融机构**: 资金提供方
5. **买方**: 房产购买方

## 核心功能需求

### 1. 客户管理模块
**功能要求**:
- 经纪公司推荐的卖方客户信息录入
- 客户基本信息管理（姓名、身份证、联系方式、房产地址等）
- 客户状态跟踪（待审核、审核中、已通过、已拒绝、已放款、已结清等）
- 客户历史业务记录查询

### 2. 资料管理与智能解析模块
**核心需求**: 使用KIMIK2 OCR技术自动提取关键信息

**需要解析的资料类型**:
- 二手房转让合同（成交价、首付款、按揭银行、交易时间等）
- 资金监管协议（监管银行、监管账户、资金流向等）
- 身份证件（夫妻双方姓名、身份证号、地址等）
- 结婚证、户口薄（婚姻状况、家庭成员等）
- 银行卡信息（还款卡号、收款卡号、开户行等）
- 不动产权属证明（房产面积、位置、价值、抵押情况等）
- 个人信用报告（信用状况、负债情况、还款记录等）

**智能解析功能**:
- 自动识别证件类型并选择对应解析模板
- 提取关键字段并结构化存储
- 数据验证和完整性检查
- 异常数据标记和人工复核提醒
- 解析结果可视化展示和手动修正

### 3. 业务风险评估模块
**评估维度**:
- **房产价值评估**: 成交价合理性、市场价格对比
- **资金流分析**: 首付款比例、按揭额度、还款能力
- **信用风险评估**: 夫妻双方信用状况、负债比例
- **交易风险评估**: 交易时间安排、资金监管安全性
- **综合风险评分**: 基于多维度数据生成风险等级

**输出结果**:
- 业务可操作性分析报告
- 风险点提示和建议
- 推荐融资额度和期限
- 风险等级评定（低风险/中风险/高风险）

### 4. 资料汇总与PDF生成模块
**功能要求**:
- 将所有解析后的资料信息汇总
- 生成标准化的业务申请PDF文档
- PDF包含客户基本信息、房产信息、资金需求、风险评估等
- 支持PDF模板自定义和格式调整
- 生成的PDF可直接提交给金融机构审批

### 5. 业务流程管理模块
**流程节点**:
1. **客户推荐**: 经纪公司推荐客户
2. **资料收集**: 收集并上传客户资料
3. **初步审查**: 我司内部审查和风险评估
4. **机构对接**: 向金融机构提交申请
5. **审批跟进**: 跟踪金融机构审批进度
6. **合同签署**: 融资服务协议、借款合同签署
7. **放款监控**: 监控放款到账情况
8. **过户跟进**: 跟踪房产过户进度
9. **回款管理**: 监控售房款回款和借款归还
10. **业务结算**: 计算服务费和返佣，完成结算

**状态管理**:
- 每个节点的状态更新和时间记录
- 节点负责人分配和任务提醒
- 异常情况预警和处理
- 流程可视化展示

### 6. 金融机构对接模块
**功能要求**:
- 多家金融机构信息管理
- 不同机构的产品参数配置（利率、期限、额度等）
- 申请提交和审批状态同步
- 放款和回款信息对接
- 机构合作数据统计分析

### 7. 财务管理模块
**收入管理**:
- 服务费收入记录和统计
- 不同金融机构的费率管理
- 收入确认和发票管理

**支出管理**:
- 经纪公司返佣计算和支付
- 返佣比例配置和调整
- 支出审批流程

**财务报表**:
- 月度/季度/年度收入统计
- 业务量和盈利分析
- 成本控制和利润分析

### 8. 报表统计模块
**业务报表**:
- 业务量统计（按时间、区域、经纪公司等维度）
- 成功率分析（审批通过率、放款成功率等）
- 平均处理时长统计
- 客户满意度调查

**风险报表**:
- 风险等级分布统计
- 逾期和坏账分析
- 风险预警报告

## 技术要求

### 前端技术栈
- **框架**: React + TypeScript + Ant Design
- **构建工具**: Vite
- **状态管理**: Redux Toolkit 或 Zustand
- **图表库**: ECharts 或 Chart.js
- **文件上传**: react-dropzone
- **PDF预览**: react-pdf

### 后端技术栈
- **框架**: Node.js + Express + TypeScript
- **数据库**: MongoDB + Redis（缓存）
- **OCR服务**: KIMIK2（主要）+ 百度OCR（备用）
- **PDF生成**: Puppeteer 或 jsPDF
- **文件存储**: 阿里云OSS 或 本地存储
- **任务队列**: Bull Queue（处理OCR任务）

### 核心技术集成
- **KIMIK2 OCR**: 
  - 身份证识别
  - 银行卡识别
  - 通用文字识别
  - 表格识别
  - 合同文档识别
- **智能解析算法**: 正则表达式 + NLP + 规则引擎
- **风险评估模型**: 基于规则的评分系统
- **工作流引擎**: 业务流程自动化

## 系统架构要求

### 整体架构
- **前后端分离**: RESTful API设计
- **微服务架构**: 按业务模块拆分服务
- **容器化部署**: Docker + Docker Compose
- **负载均衡**: Nginx反向代理
- **数据备份**: 定期数据备份和恢复

### 安全要求
- **数据加密**: 敏感数据加密存储
- **访问控制**: 基于角色的权限管理
- **审计日志**: 操作日志记录和审计
- **数据脱敏**: 敏感信息展示脱敏
- **合规性**: 符合金融数据保护要求

## 用户角色和权限

### 角色定义
1. **系统管理员**: 系统配置、用户管理、数据维护
2. **业务经理**: 业务审批、风险控制、流程监控
3. **业务员**: 客户对接、资料收集、流程跟进
4. **财务人员**: 财务管理、结算处理、报表查看
5. **经纪公司**: 客户推荐、进度查询（外部用户）

### 权限控制
- 基于角色的功能权限控制
- 数据权限控制（只能查看负责的客户）
- 操作权限控制（审批、修改、删除等）
- 敏感数据访问权限控制

## 性能要求

### 响应时间
- 页面加载时间 < 3秒
- OCR识别时间 < 30秒
- PDF生成时间 < 10秒
- 数据查询响应 < 2秒

### 并发处理
- 支持100+并发用户
- OCR任务队列处理
- 大文件上传支持
- 系统稳定性保障

## 部署和运维

### 部署环境
- **开发环境**: 本地Docker环境
- **测试环境**: 云服务器测试部署
- **生产环境**: 高可用集群部署
- **备份环境**: 数据备份和灾难恢复

### 监控告警
- **系统监控**: CPU、内存、磁盘、网络
- **应用监控**: 接口响应时间、错误率
- **业务监控**: 业务量、成功率、异常情况
- **告警机制**: 邮件、短信、钉钉通知

## 项目交付要求

### 交付物
1. **系统源代码**: 前端 + 后端完整代码
2. **部署文档**: 详细的部署和配置说明
3. **用户手册**: 各角色的操作指南
4. **技术文档**: 系统架构和接口文档
5. **测试报告**: 功能测试和性能测试报告

### 验收标准
- 所有功能模块正常运行
- OCR识别准确率 > 90%
- 系统性能满足要求
- 安全测试通过
- 用户培训完成

请基于以上需求，提供详细的技术实现方案、开发计划和成本估算。
